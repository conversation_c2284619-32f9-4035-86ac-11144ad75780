services:
  portfolio:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: portfolio-app
    ports:
      - "8068:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://127.0.0.1/"]
      interval: 15s
      timeout: 5s
      retries: 2
      start_period: 5s
    networks:
      - portfolio-network

networks:
  portfolio-network:
    driver: bridge
