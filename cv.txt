<PERSON> Faizan <PERSON>
Senior Network/System Administrator
Over 17 years of Kuwait experience
📧 <EMAIL> | 📱 +965-51531519
🌐 Portfolio: https://portfolio.mcsaguru.com
💼 LinkedIn: linkedin.com/in/muhammad-faizan-muhammad-hanif-51403a65/
🌐Blog: https://mcsaguru.com
________________________________________________________________________________________________

CAREER OBJECTIVE

Results-driven IT Infrastructure specialist with over 17 years of experience in enterprise environments. Proven expertise in designing, implementing, and maintaining complex IT systems with a focus on high availability and security. Seeking to leverage extensive experience in systems administration, network management, and automation to contribute to a challenging technical leadership role while driving innovation and operational excellence

AI & Machine Learning Integration:

Over the past few years, I've been working with AI tools and technologies to make my daily IT work more efficient and automated. 

Here's what I've been doing:

AI-Based Scripts and Automation:

• Created Python scripts using basic machine learning to predict when servers might have issues
• Built simple automation tools that can learn from network patterns and adjust settings automatically
• Used AI APIs to create chatbots that help users with common IT problems
• Made scripts that can read log files and automatically categorize different types of errors

AI Tools I Work With:

• ChatGPT and OpenAI APIs - for creating automated responses and documentation
• Python with basic ML libraries - like scikit-learn for simple predictions
• Microsoft AI services - integrated some AI features into our Office 365 setup
• Google AI tools - used for analyzing network data and creating reports

Real Solutions I've Built:

• Smart Monitoring - created a system that learns normal network behavior and alerts me when something unusual happens
• Automated Troubleshooting - built tools that can fix common problems automatically based on what they've learned from past issues
• Intelligent Reporting - made systems that can write basic technical reports by analyzing system data
• User Support Automation - created simple AI helpers that can answer basic IT questions from users

What I Can Do:

• Write Python scripts that use AI to solve IT problems
• Integrate AI services into existing systems
• Create automated solutions that learn and improve over time
• Build simple machine learning models for network and system monitoring
• Use AI tools to make documentation and reporting more efficient
Skills & Technologies:
Comprehensive technical expertise across multiple domains

Senior Systems Administration
• Operating Systems: Windows Server 2012-2022, Ubuntu Linux, CentOS
• Active Directory: Domain Services, Group Policy, FSMO Roles, Site Replication
• Exchange Server: Hybrid Deployment, High Availability, Mail Flow, Security
• Microsoft 365: Exchange Online, Teams, SharePoint, Azure AD, Intune
• Virtualization: VMware vSphere, Hyper-V, Proxmox, Docker
• Backup Solutions: Veeam Backup & Replication, Windows Server Backup
• Storage Systems: Dell EMC, Synology NAS, iSCSI, Fiber Channel
• Monitoring Tools: PRTG, Nagios, Custom Monitoring Solutions

Network Infrastructure
• Routing & Switching: Cisco Catalyst Switches, VLANs, Spanning Tree, OSPF
• Security: Fortigate Firewalls, VPN, IPS/IDS, Access Control Lists
• Wireless: Cisco WLC, Access Points, RF Planning, WiFi Security
• Network Protocols: TCP/IP, DNS, DHCP, SNMP, RADIUS
• Load Balancing: Layer 4-7 Load Balancing, High Availability

Security
• Firewall Management: FortiGate, Policy Management, VPN Configuration
• Access Control: Role-Based Access Control (RBAC), Multi-Factor Authentication
• Compliance: Security Auditing, Vulnerability Assessment
• Video Surveillance: Hikvision Systems, IP Camera Networks
• Security Tools: Nmap, Wireshark, Security Event Management

Programming & Automation
• Scripting: PowerShell, Python, Bash
• Web Technologies: HTML, CSS, JavaScript, PHP
• Version Control: Git, GitHub
• API Integration: REST APIs, JSON, XML
• Automation Tools: Task Scheduler, Cron Jobs

Database Management
• SQL Server Administration
• MySQL/MariaDB
• Database Backup and Recovery
• Performance Tuning

Project Management
• IT Infrastructure Planning
• System Implementation
• Vendor Management
• Technical Documentation
• Budget Planning
• Risk Management

Additional Skills
• Technical Writing and Documentation
• IT Training and Staff Development
• Problem-Solving and Troubleshooting
• Team Leadership
• Change Management
• Disaster Recovery Planning


PROFESSIONAL EXPERIENCE

Senior Systems & Network Administrator		
American International School of Kuwait
(June 2015 till Present)

As the Senior Systems & Network Administrator, I am responsible for overseeing and managing the entire IT infrastructure of one of Kuwait's premier educational institutions. My comprehensive responsibilities include the following key areas:


Server Infrastructure Management

• Architect and maintain enterprise Windows Server infrastructure (2008-2022), including:
  - Implementation and management of Active Directory services across multiple domains
  - Design and maintenance of Group Policy Objects (GPOs) for security and compliance
  - Configuration and optimization of DHCP and DNS services for 4000+ network clients
  - Regular security patch management and system updates across server infrastructure
  - Implementation of disaster recovery protocols and business continuity planning
  - Performance monitoring and capacity planning for critical server resources

Email and Collaboration Systems

• Successfully designed and implemented hybrid mail infrastructure:
  - Managing on-premises Exchange servers with high availability configuration
  - Integration with Office 365 for seamless hybrid mail flow
  - Implementation of email security protocols (SPF, DKIM, DMARC)
  - Migration of user mailboxes between on-premises and cloud environments
  - Management of Microsoft Teams, SharePoint, and other O365 services
  - Development of PowerShell scripts for automated user provisioning and license management

Virtualization and Container Management

• Lead virtualization initiatives across multiple platforms:
  - VMware vSphere environment with HA/DRS configurations
  - Hyper-V clusters with failover capabilities
  - Proxmox VE implementation for Linux-based workloads
  - Docker container deployment and orchestration
  - Resource allocation and performance optimization
  - VM lifecycle management and template maintenance
  - P2V migrations and workload balancing





Network Infrastructure

• Comprehensive management of enterprise network infrastructure:
  - Design and implementation of campus-wide network architecture
  - Configuration and maintenance of Cisco switching infrastructure with redundant paths
  - Implementation of VLANs for network segmentation and security
  - Switch stacking and virtual chassis configurations for high availability
  - Regular firmware updates and security patch management
  - Network performance monitoring and optimization
  - Documentation of network topology and change management
  - Implementation and management of IP telephony infrastructure
  - VPN solution design and implementation:
    * Fortigate SSL VPN and IPSec configuration
    * Wireguard VPN server deployment and management
    * Headscale server implementation for secure remote access
    * Client VPN configuration and troubleshooting
    * Split-tunnel VPN configuration
    * Multi-factor authentication integration
    * VPN monitoring and bandwidth management

Security and Firewall Management

• Oversee enterprise security infrastructure:
  - Configuration and maintenance of Fortigate firewall clusters
  - Implementation of security policies and access control lists
  - VPN configuration for remote access and site-to-site connectivity
  - Web filtering and application control policies
  - IPS/IDS management and threat prevention
  - Security log analysis and incident response
  - Regular security audits and compliance checks

Wireless Network Administration

• Manage enterprise wireless infrastructure:
  - Administration of Cisco Wireless Controllers in HA configuration
  - Access Point deployment and coverage optimization
  - Implementation of multiple SSIDs with different security policies
  - Guest network management and captive portal configuration
  - RF optimization and channel planning
  - Wireless network monitoring and troubleshooting

Storage and Backup Solutions

• Manage enterprise storage infrastructure:
  - Administration of Dell EMC storage arrays and Synology NAS systems
  - LUN creation, mapping, and storage provisioning
  - Implementation of storage tiering for optimal performance
  - Configuration of iSCSI and fiber channel connectivity
  - Storage capacity planning and performance monitoring
  - Veeam Backup & Replication implementation for:
    * Physical server backup and recovery
    * Virtual machine backup with instant recovery capabilities
    * Regular backup testing and verification
    * Offsite backup replication

Custom Solutions and Automation

• Developed multiple enterprise-grade solutions:
  - Network Monitoring System:
    * Real-time monitoring of all network devices
    * Automated alert system for device status changes
    * Custom dashboard for network health visualization
    * Integration with email and SMS alert systems
  - Switch Management Portal:
    * Web-based interface for switch configuration visualization
    * Real-time port status monitoring
    * VLAN management and documentation
    * Connected device tracking and reporting
  - Automated Backup System:
    * Daily configuration backup of all network devices
    * Version control for configuration files
    * Automated verification of backup success

Security Camera Infrastructure

• Implemented and maintain extensive surveillance system:
  - Management of 400+ Hikvision IP cameras
  - Design and implementation of storage solution for video retention
  - Configuration of motion detection and alert systems
  - Implementation of user access controls and audit logging
  - Regular maintenance and firmware updates
  - Integration with network infrastructure

Professional Development & Knowledge Sharing

• Founder and Lead Author at MCSAGuru.com (Technical Blog):
  - Author comprehensive technical articles and tutorials focusing on:
    * Windows Server administration best practices
    * Network infrastructure solutions
    * Cloud technology implementations
    * PowerShell scripting guides
    * System automation techniques
  - Share real-world experience and solutions with the IT community
  - Publish regular content on emerging technologies and industry trends
  - Demonstrate thought leadership in system administration and networking
  - Build and engage with a community of IT professionals
  - Document practical solutions to complex technical challenges

Key Achievements:

• Designed and implemented comprehensive disaster recovery plan reducing potential downtime from days to hours
• Achieved 75% reduction in system incidents through proactive monitoring and automation
• Reduced IT operational costs by 40% through effective resource utilization and automation
• Improved network security posture with zero major security incidents over 5 years
• Successfully managed IT infrastructure growth from supporting 1000 to 4000+ users
• Implemented automated solutions saving 100+ hours of manual work monthly
• Maintained 99.99% uptime for critical systems through high availability configurations
• Successfully maintained 99.9% uptime for Student Management System during peak academic periods
• Created comprehensive documentation and knowledge base through blog articles, benefiting both internal team and global IT community


Computer Troubleshooters (Kuwait) – (5th November 2009 till June 2015)
(Over 5 years of experience)

Computer Troubleshooters is the world’s number one franchise network for computer services having the full range of computer operation whether individual users, small to medium users, small to medium businesses or large organizations.

Job role as Network/System Administrator in Computer Troubleshooters:

Under general direction designs, supports, maintains, and evaluates computer networking and server configu-ration; installs, configures, and maintains both physical and virtual computer servers; maintains employee network, e-mail; performs other related duties as assigned.

•	Responsible for managing and troubleshooting domain controllers.
•	Provide technical support for network administrators and end-users - servers and workstations
Including DHCP, WINS, DNS, file and print servers, various MS operating systems and office suite.
•	Perform administration tasks such as creating new user accounts, setup permissions and group
membership, share network resources, setup network printers.
•	Preparing PCs with required hardware / software configuration, in addition to installation / updating client software on all user’ PCs in accordance with procedures developed by IT Security and Development Cen-ter.
•	Establishes network specifications by conferring with users; analyzing workflow, access, information, and security requirements.
•	Establishes network by evaluating network performance issues including availability, utilization, and latency; planning and executing the selection, installation, configuration, and testing of equipment; defin-ing network policies and procedures; establishing connections and firewalls.
•	Maintains network performance by performing network monitoring and analysis, and performance tuning; troubleshooting network problems; escalating problems to vendor.
•	Prepares users by designing and conducting training programs; providing references and support.
•	Upgrades network by conferring with vendors; developing, testing, evaluating, and installing enhance-ments.
•	Meets financial requirements by submitting information for budgets; monitoring expenses.
•	Updates job knowledge by participating in educational opportunities; reading professional publications; maintaining personal networks; participating in professional organizations.
•	Protects organization's value by keeping information confidential.
•	Perform software and security upgrades.
•	Perform troubleshooting of computer systems and related equipment.
•	Technical Assistant for LAN/WAN Network for any system critical warnings.

Kellogg Brown and Root (Kuwait) – (November 2007 – September 2009)

KBR is one of the world's premiere engineering, procurement and construction companies with approximately 27,000 employees in over 70 countries on five continents.

Job role as Network/System Administrator in Kellogg Brown and Root:

Supervised and maintained daily network operations, including internet/intranet site admin-istration, backup, disaster recovery, virus-protection, e-mail, and security. Researched, planned, developed, and implemented overall integrated LAN/WAN system design, including soft-ware/hardware component specifications, upgrades, configuration, and expansion. Monitored and diagnosed network system-level issues of performance, speed, reliability, and accessibility; Installed, upgraded, and configured routers, switches, and hubs.

•	Provided Level III in-house support to technical staff and end-users
•	Planned project management of infrastructure for NT, Netware, and Citrix LAN/WAN                                    networks, including design, analysis, evaluation, installation, and maintenance.
•	Ensured high user satisfaction through personal consultation and issue follow-up for comprehensive resolutions. 
•	Resolved problems related to collision, data traffic congestion, LAN segmentation, and network cable standards.
•	Trained less senior technicians and support specialists on system configuration, client access, and troubleshooting skills. 
•	Provide technical support for network administrators and end-users - servers and workstations
Including DHCP, WINS, DNS, file and print servers, various MS operating systems and office suite.
•	Perform administration tasks such as creating new user accounts, setup permissions and group
membership, share network resources, setup network printers.







Education & Professional Development:						
					
Formal Education

Diploma in Advanced Networks Management
  
Allied Computer International (Focused on enterprise networking and infrastructure management)

Diploma in Hardware and Maintenance

Government College Lahore (Computer hardware, troubleshooting, and system maintenance)

Intermediate (MAO College Lahore) General education foundation.

How I Really Learned My Skills

To be honest, most of what I know about IT came from actually doing the work, not from classrooms. Over
17 years, I've learned by:

Hands-On Experience

• Every problem I solved taught me something new
• Made mistakes and learned from them - that's how you really understand technology
• Worked with different systems and figured out how they work by actually using them
• Built things from scratch and learned what works and what doesn't

Self-Learning Approach

• When I needed to learn something new, I would set up a test environment and try it myself
• Read technical documentation and then actually implemented what I learned
• Watched online tutorials, but more importantly, practiced everything myself
• Joined online communities where IT professionals share real-world solutions

Staying Current

• Technology changes fast, so I'm always learning new things
• When new versions of software come out, I test them in my lab before using them at work
• Follow IT blogs and forums to see what problems other people are solving
• Try new tools and technologies to see if they can make my work better

Why This Works Better

• Real-world experience teaches you things you can't learn from books
• When you actually implement something, you understand all the little details that matter
• You learn to troubleshoot problems that don't have textbook answers
• You develop the ability to adapt and learn new technologies quickly



Continuous Learning

• Every day at work is a learning opportunity
• New challenges require new solutions, which means learning new skills
• I enjoy figuring out how things work and finding better ways to do things
• The IT field changes so much that continuous learning isn't optional - it's necessary

What This Means

My education gave me a foundation, but my real expertise comes from 17 years of solving actual IT
problems in real business environments. This practical experience has taught me not just how to
use technology, but how to make it work reliably for thousands of users every day.

I believe that hands-on experience, combined with a willingness to keep learning, is more valuable
than just having certificates. The proof is in the results - maintaining 99.99% uptime for critical
systems and successfully managing infrastructure for 4000+ users.

Personal Information

Full Name				Muhammad Faizan Hanif
Father Name				Muhammad Hanif
Email Address				<EMAIL>
Mobile Number				+965-51531519 , +965 97861303
Religion					Islam
Date of Birth				November 08, 1986
Visa Article				18
Passport No				*********
Marital Status				Single
Nationality				Pakistani 
Driving License                                              Valid Kuwaiti driving License
