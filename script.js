// ===== PORTFOLIO WEBSITE JAVASCRIPT =====

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initSkillsSection();
    initExperienceSection();
    initScrollAnimations();
    initSmoothScrolling();
    initActiveNavigation();
    initNameAnimation();
    initExperienceAnimations();
});

// ===== NAVIGATION FUNCTIONALITY =====
function initNavigation() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-menu a');

    // Toggle mobile menu
    navToggle.addEventListener('click', function() {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
        document.body.classList.toggle('nav-open');
    });

    // Close menu when clicking on nav links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.classList.remove('nav-open');
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.classList.remove('nav-open');
        }
    });

    // Navbar scroll effect - maintain dark theme
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(26, 32, 44, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
        } else {
            navbar.style.background = 'rgba(26, 32, 44, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
}

// ===== SKILLS SECTION DYNAMIC CONTENT =====
function initSkillsSection() {
    const skillsData = {
        'AI & Machine Learning': {
            icon: 'fas fa-robot',
            skills: [
                'Python with ML libraries (scikit-learn)',
                'ChatGPT and OpenAI APIs',
                'Microsoft AI services integration',
                'Google AI tools for data analysis',
                'Automated troubleshooting systems',
                'Intelligent monitoring solutions',
                'AI-powered reporting tools',
                'Machine learning for predictive maintenance'
            ]
        },
        'Systems Administration': {
            icon: 'fas fa-server',
            skills: [
                'Windows Server 2012-2022',
                'Ubuntu Linux, CentOS',
                'Active Directory Services',
                'Exchange Server & Office 365',
                'VMware vSphere, Hyper-V, Proxmox',
                'Docker containerization',
                'Veeam Backup & Replication',
                'Dell EMC & Synology storage'
            ]
        },
        'Network Infrastructure': {
            icon: 'fas fa-network-wired',
            skills: [
                'Cisco Catalyst Switches',
                'VLANs & Spanning Tree',
                'OSPF routing protocols',
                'Fortigate Firewalls',
                'VPN configuration',
                'Cisco Wireless Controllers',
                'TCP/IP, DNS, DHCP',
                'Load balancing solutions'
            ]
        },
        'Security & Compliance': {
            icon: 'fas fa-shield-alt',
            skills: [
                'Firewall management',
                'Multi-factor authentication',
                'Security auditing',
                'Vulnerability assessment',
                'Hikvision surveillance systems',
                'Access control systems',
                'Compliance monitoring',
                'Incident response'
            ]
        },
        'Programming & Automation': {
            icon: 'fas fa-code',
            skills: [
                'PowerShell scripting',
                'Python automation',
                'Bash scripting',
                'HTML, CSS, JavaScript',
                'PHP web development',
                'Git version control',
                'REST API integration',
                'Task automation'
            ]
        },
        'Database & Project Management': {
            icon: 'fas fa-database',
            skills: [
                'SQL Server administration',
                'MySQL/MariaDB',
                'Database backup & recovery',
                'Performance tuning',
                'IT infrastructure planning',
                'Vendor management',
                'Technical documentation',
                'Risk management'
            ]
        }
    };

    const skillsGrid = document.querySelector('.skills-grid');

    Object.entries(skillsData).forEach(([category, data]) => {
        const skillCategory = document.createElement('div');
        skillCategory.className = 'skill-category';
        skillCategory.innerHTML = `
            <h3><i class="${data.icon}" aria-hidden="true"></i>${category}</h3>
            <ul class="skill-list">
                ${data.skills.map(skill => `<li>${skill}</li>`).join('')}
            </ul>
        `;
        skillsGrid.appendChild(skillCategory);
    });
}

// ===== EXPERIENCE SECTION DYNAMIC CONTENT =====
function initExperienceSection() {
    const experienceData = [
        {
            title: 'Senior Systems & Network Administrator',
            company: 'American International School of Kuwait',
            period: 'June 2015 - Present',
            description: 'Leading IT infrastructure management for 4000+ users with 99.99% uptime achievement.',
            achievements: [
                'Designed and implemented hybrid Exchange infrastructure with on-premises and Office 365 integration',
                'Developed Python/Django network monitoring system for 300+ Cisco access points, 50+ switches, and 400+ IP cameras',
                'Built web-based GUI application for managing Cisco switches without native GUI interfaces',
                'Implemented Docker containerization infrastructure reducing hardware costs by 40%',
                'Created PowerShell scripts for Active Directory automation saving 100+ hours monthly',
                'Managed VMware vSphere, Hyper-V, and Proxmox virtualization platforms',
                'Implemented comprehensive backup and disaster recovery solutions using Veeam',
                'Maintained 99.99% uptime for critical systems through high availability configurations',
                'Designed and implemented campus-wide network architecture with redundant paths',
                'Managed Cisco Wireless Controllers with HA configuration and 400+ access points',
                'Configured and maintained Fortigate firewall clusters with IPS/IDS and VPN solutions',
                'Implemented security protocols including SPF, DKIM, DMARC for email security'
            ]
        },
        {
            title: 'Network/System Administrator',
            company: 'Computer Troubleshooters Kuwait',
            period: 'November 2009 - June 2015',
            description: 'Provided comprehensive IT support and infrastructure management for diverse client base.',
            achievements: [
                'Managed Windows Server domain controllers with Active Directory and Group Policy',
                'Provided technical support for DHCP, DNS, file and print servers across multiple MS operating systems',
                'Created user accounts, configured permissions, and managed network resources',
                'Evaluated network performance issues including availability, utilization, and latency',
                'Installed, configured, and tested networking equipment including routers, switches, and hubs',
                'Maintained network performance through monitoring, analysis, and performance tuning',
                'Designed and conducted training programs for technical staff and end-users',
                'Performed software and security upgrades across client infrastructure',
                'Implemented network security measures and access control policies',
                'Troubleshot complex network problems and escalated to vendors when necessary'
            ]
        },
        {
            title: 'Network/System Administrator',
            company: 'Kellogg Brown and Root Kuwait',
            period: 'November 2007 - September 2009',
            description: 'Supervised daily network operations and infrastructure development for global engineering firm.',
            achievements: [
                'Managed daily network operations including internet/intranet, backup, and disaster recovery',
                'Researched, planned, and implemented integrated LAN/WAN system designs',
                'Monitored and diagnosed network system-level issues of performance, speed, and reliability',
                'Installed, upgraded, and configured routers, switches, and hubs',
                'Provided Level III in-house support to technical staff and end-users',
                'Planned project management of infrastructure for NT, Netware, and Citrix networks',
                'Resolved problems related to collision, data traffic congestion, and network segmentation',
                'Trained less senior technicians and support specialists on system configuration and troubleshooting',
                'Managed email, security, and virus protection systems',
                'Ensured high user satisfaction through comprehensive issue resolution'
            ]
        }
    ];

    const timeline = document.querySelector('.timeline');

    experienceData.forEach((job, index) => {
        const timelineItem = document.createElement('div');
        timelineItem.className = 'timeline-item';
        timelineItem.innerHTML = `
            <div class="timeline-content">
                <div class="timeline-date">${job.period}</div>
                <h3>${job.title}</h3>
                <h4>${job.company}</h4>
                <p>${job.description}</p>
                <ul>
                    ${job.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
                </ul>
            </div>
            <div class="timeline-dot"></div>
        `;
        timeline.appendChild(timelineItem);
    });
}

// ===== ENHANCED EXPERIENCE ANIMATIONS =====
function initExperienceAnimations() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    // Add staggered animation delays
    timelineItems.forEach((item, index) => {
        item.style.transitionDelay = `${index * 0.2}s`;
    });
    
    // Intersection Observer for timeline items
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    timelineItems.forEach(item => {
        observer.observe(item);
    });
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.skill-category, .highlight-item, .contact-item, .project-card, .education-card, .learning-method, .highlight-card');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';
        observer.observe(el);
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for fixed navbar

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== ACTIVE NAVIGATION =====
function initActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');

    function updateActiveNav() {
        let current = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.offsetHeight;

            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }

    window.addEventListener('scroll', updateActiveNav);
    updateActiveNav(); // Initial call
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Debounce function for scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    // ESC key closes mobile menu
    if (e.key === 'Escape') {
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');

        if (navMenu.classList.contains('active')) {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
            document.body.classList.remove('nav-open');
        }
    }

    // Enter key activates nav toggle
    if (e.key === 'Enter' && e.target.classList.contains('nav-toggle')) {
        e.target.click();
    }
});

// ===== CONTACT FORM ENHANCEMENT (if needed in future) =====
function initContactForm() {
    const contactForm = document.querySelector('#contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Add form validation and submission logic here
            const formData = new FormData(this);

            // Show success message
            showNotification('Message sent successfully!', 'success');
        });
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: var(--primary-color);
        color: white;
        border-radius: var(--border-radius-md);
        box-shadow: var(--shadow-medium);
        z-index: var(--z-tooltip);
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// ===== ERROR HANDLING =====
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
    // Could implement error reporting here
});

// ===== INITIALIZATION =====
// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initLazyLoading();
    initContactForm();
});

// ===== UTILITY FUNCTIONS =====
// Check if element is in viewport
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// Smooth scroll to element
function scrollToElement(element, offset = 70) {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
    });
}

// ===== NAME ANIMATION =====
function initNameAnimation() {
    const heroName = document.querySelector('.hero-name');

    if (heroName) {
        const originalText = heroName.textContent;
        heroName.textContent = '';
        heroName.classList.add('typing');

        let i = 0;
        const typeWriter = () => {
            if (i < originalText.length) {
                heroName.textContent += originalText.charAt(i);
                i++;
                setTimeout(typeWriter, 100); // Adjust speed here (100ms per character)
            } else {
                // Remove cursor after typing is complete
                setTimeout(() => {
                    heroName.classList.remove('typing');
                }, 1000);
            }
        };

        // Start typing after initial delay
        setTimeout(typeWriter, 1000);
    }
}